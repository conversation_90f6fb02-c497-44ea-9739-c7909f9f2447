"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import type { User } from 'database';
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Badge } from "@ui/components/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@ui/components/tabs";
import {
	FileText,
	Upload,
	Download,
	Search,
	Plus,
	Calendar,
	User as UserIcon,
	Eye,
	Edit,
	Trash2,
	AlertCircle,
	RefreshCw
} from "lucide-react";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@ui/components/dialog";

interface Prescription {
	id: string;
	appointmentId: string;
	patientName: string;
	patientId: string;
	doctorName: string;
	createdAt: string;
	status: 'active' | 'cancelled' | 'expired';
	pdfUrl?: string;
	memedId?: string;
	content?: any;
}

interface Appointment {
	id: string;
	patientName: string;
	patientId: string;
	doctorName: string;
	scheduledAt: string;
	status: string;
	symptoms?: string;
	consultType: string;
	duration: number;
}

interface DoctorPrescriptionsClientProps {
	user: User;
}

export function DoctorPrescriptionsClient({ user }: DoctorPrescriptionsClientProps) {
	const router = useRouter();
	const [prescriptions, setPrescriptions] = useState<Prescription[]>([]);
	const [appointments, setAppointments] = useState<Appointment[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [isLoadingAppointments, setIsLoadingAppointments] = useState(false);
	const [searchTerm, setSearchTerm] = useState("");
	const [selectedFile, setSelectedFile] = useState<File | null>(null);
	const [uploadingPrescription, setUploadingPrescription] = useState<string | null>(null);
	const [showCreateDialog, setShowCreateDialog] = useState(false);

	// Carregar prescrições da API
	const loadPrescriptions = async () => {
		setIsLoading(true);
		try {
			const response = await fetch('/api/prescriptions/doctor');
			if (!response.ok) {
				throw new Error('Erro ao carregar prescrições');
			}
			const data = await response.json();
			setPrescriptions(data.prescriptions || []);
		} catch (error) {
			console.error('Erro ao carregar prescrições:', error);
			toast.error('Erro ao carregar prescrições');
			// Não usar dados mock - deixar vazio para mostrar estado real
			setPrescriptions([]);
		} finally {
			setIsLoading(false);
		}
	};

	useEffect(() => {
		loadPrescriptions();
	}, [user.name]);

	// Carregar consultas sem prescrição
	const loadAppointmentsWithoutPrescription = async () => {
		setIsLoadingAppointments(true);
		try {
			const response = await fetch('/api/appointments/without-prescription');
			if (!response.ok) {
				throw new Error('Erro ao carregar consultas');
			}
			const data = await response.json();
			setAppointments(data.appointments || []);
		} catch (error) {
			console.error('Erro ao carregar consultas:', error);
			toast.error('Erro ao carregar consultas disponíveis');
			setAppointments([]);
		} finally {
			setIsLoadingAppointments(false);
		}
	};

	const filteredPrescriptions = prescriptions.filter(prescription =>
		prescription.patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
		prescription.id.toLowerCase().includes(searchTerm.toLowerCase())
	);

	const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0];
		if (file && file.type === 'application/pdf') {
			setSelectedFile(file);
		} else {
			toast.error("Por favor, selecione apenas arquivos PDF");
		}
	};

	const handleUploadPDF = async (prescriptionId: string) => {
		if (!selectedFile) {
			toast.error("Selecione um arquivo PDF primeiro");
			return;
		}

		setUploadingPrescription(prescriptionId);

		try {
			const formData = new FormData();
			formData.append('pdf', selectedFile);

			const response = await fetch(`/api/prescriptions/${prescriptionId}/upload-pdf`, {
				method: 'POST',
				body: formData
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Erro ao fazer upload');
			}

			const data = await response.json();

			// Atualizar prescrição com PDF
			setPrescriptions(prev => prev.map(p =>
				p.id === prescriptionId
					? { ...p, pdfUrl: data.pdfUrl }
					: p
			));

			setSelectedFile(null);
			toast.success("PDF anexado com sucesso!");
		} catch (error) {
			console.error('Erro no upload:', error);
			toast.error(error instanceof Error ? error.message : "Erro ao anexar PDF");
		} finally {
			setUploadingPrescription(null);
		}
	};

	const handleDownloadPDF = (pdfUrl: string, patientName: string) => {
		if (!pdfUrl) return;

		// Simular download
		const link = document.createElement('a');
		link.href = pdfUrl;
		link.download = `prescricao-${patientName.replace(/\s+/g, '-').toLowerCase()}.pdf`;
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);

		toast.success("Download iniciado");
	};

	const handleCreatePrescription = (appointmentId: string) => {
		router.push(`/app/appointments/${appointmentId}/prescription`);
		setShowCreateDialog(false);
	};

	const handleOpenCreateDialog = () => {
		setShowCreateDialog(true);
		loadAppointmentsWithoutPrescription();
	};

	const getStatusBadge = (status: string) => {
		switch (status) {
			case 'active':
				return <Badge variant="default">Ativa</Badge>;
			case 'expired':
				return <Badge variant="secondary">Expirada</Badge>;
			case 'cancelled':
				return <Badge variant="destructive">Cancelada</Badge>;
			default:
				return <Badge variant="outline">{status}</Badge>;
		}
	};

	if (isLoading) {
		return (
			<div className="flex items-center justify-center min-h-[400px]">
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
					<p className="text-gray-600">Carregando prescrições...</p>
				</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto px-4 py-6 space-y-6">
			{/* Header */}
			<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
				<div>
					<h1 className="text-2xl font-bold text-gray-900">
						Gerenciar Prescrições
					</h1>
					<p className="text-gray-600">
						Visualize, edite e anexe PDFs às suas prescrições
					</p>
				</div>
				<div className="flex items-center gap-2">
					<Button
						variant="outline"
						onClick={loadPrescriptions}
						disabled={isLoading}
						className="flex items-center gap-2"
					>
						<RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
						Atualizar
					</Button>

					<Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
					<DialogTrigger asChild>
						<Button onClick={handleOpenCreateDialog} className="flex items-center gap-2">
							<Plus className="w-4 h-4" />
							Nova Prescrição
						</Button>
					</DialogTrigger>
					<DialogContent className="max-w-2xl">
						<DialogHeader>
							<DialogTitle>Criar Nova Prescrição</DialogTitle>
						</DialogHeader>
						<div className="space-y-4">
							<p className="text-sm text-gray-600">
								Selecione uma consulta para criar a prescrição:
							</p>

							{isLoadingAppointments ? (
								<div className="flex items-center justify-center py-8">
									<div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
									<span className="ml-2 text-sm text-gray-600">Carregando consultas...</span>
								</div>
							) : appointments.length === 0 ? (
								<div className="text-center py-8">
									<AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
									<h3 className="text-lg font-medium text-gray-900 mb-2">
										Nenhuma consulta disponível
									</h3>
									<p className="text-gray-600">
										Não há consultas concluídas sem prescrição para criar receitas.
									</p>
								</div>
							) : (
								<div className="max-h-96 overflow-y-auto space-y-2">
									{appointments.map((appointment) => (
										<Card
											key={appointment.id}
											className="cursor-pointer hover:shadow-md transition-shadow"
											onClick={() => handleCreatePrescription(appointment.id)}
										>
											<CardContent className="p-4">
												<div className="flex items-center justify-between">
													<div>
														<h4 className="font-medium text-gray-900">
															{appointment.patientName}
														</h4>
														<p className="text-sm text-gray-600">
															{new Date(appointment.scheduledAt).toLocaleDateString('pt-BR')} às{' '}
															{new Date(appointment.scheduledAt).toLocaleTimeString('pt-BR', {
																hour: '2-digit',
																minute: '2-digit'
															})}
														</p>
														{appointment.symptoms && (
															<p className="text-xs text-gray-500 mt-1">
																Sintomas: {appointment.symptoms}
															</p>
														)}
													</div>
													<Badge variant="outline">
														{appointment.status}
													</Badge>
												</div>
											</CardContent>
										</Card>
									))}
								</div>
							)}
						</div>
					</DialogContent>
				</Dialog>
				</div>
			</div>

			{/* Search */}
			<Card>
				<CardContent className="pt-6">
					<div className="relative">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
						<Input
							placeholder="Buscar por paciente ou ID da prescrição..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="pl-10"
						/>
					</div>
				</CardContent>
			</Card>

			{/* Tabs */}
			<Tabs defaultValue="all" className="space-y-4">
				<TabsList>
					<TabsTrigger value="all">Todas ({prescriptions.length})</TabsTrigger>
					<TabsTrigger value="active">
						Ativas ({prescriptions.filter(p => p.status === 'active').length})
					</TabsTrigger>
					<TabsTrigger value="pending">
						Sem PDF ({prescriptions.filter(p => !p.pdfUrl).length})
					</TabsTrigger>
				</TabsList>

				<TabsContent value="all" className="space-y-4">
					{filteredPrescriptions.length === 0 ? (
						<Card>
							<CardContent className="py-12 text-center">
								<FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
								<h3 className="text-lg font-medium text-gray-900 mb-2">
									Nenhuma prescrição encontrada
								</h3>
								<p className="text-gray-600 mb-4">
									{searchTerm ? "Tente ajustar sua busca" : "Você ainda não criou nenhuma prescrição"}
								</p>
								{!searchTerm && (
									<Button
										onClick={handleOpenCreateDialog}
										className="flex items-center gap-2 mx-auto"
									>
										<Plus className="w-4 h-4" />
										Criar Primeira Prescrição
									</Button>
								)}
							</CardContent>
						</Card>
					) : (
						<div className="grid gap-4">
							{filteredPrescriptions.map((prescription) => (
								<Card key={prescription.id} className="hover:shadow-md transition-shadow">
									<CardContent className="p-6">
										<div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
											{/* Info Principal */}
											<div className="flex-1 space-y-2">
												<div className="flex items-center gap-3">
													<UserIcon className="w-5 h-5 text-gray-500" />
													<span className="font-medium text-gray-900">
														{prescription.patientName}
													</span>
													{getStatusBadge(prescription.status)}
												</div>

												<div className="flex items-center gap-3 text-sm text-gray-600">
													<Calendar className="w-4 h-4" />
													<span>
														{new Date(prescription.createdAt).toLocaleDateString('pt-BR')}
													</span>
													<span>•</span>
													<span>ID: {prescription.id}</span>
												</div>

												{prescription.memedId && (
													<div className="text-sm text-blue-600">
														Criada via Memed (ID: {prescription.memedId})
													</div>
												)}
											</div>

											{/* Ações */}
											<div className="flex flex-col sm:flex-row gap-2 lg:w-auto w-full">
												{prescription.pdfUrl ? (
													<>
														<Button
															variant="outline"
															size="sm"
															onClick={() => handleDownloadPDF(prescription.pdfUrl!, prescription.patientName)}
															className="flex items-center gap-2"
														>
															<Download className="w-4 h-4" />
															Download PDF
														</Button>
														<Button
															variant="outline"
															size="sm"
															className="flex items-center gap-2"
														>
															<Eye className="w-4 h-4" />
															Visualizar
														</Button>
													</>
												) : (
													<div className="flex flex-col sm:flex-row gap-2">
														<div className="flex items-center gap-2">
															<input
																type="file"
																accept=".pdf"
																onChange={handleFileSelect}
																className="hidden"
																id={`file-${prescription.id}`}
															/>
															<label
																htmlFor={`file-${prescription.id}`}
																className="cursor-pointer inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-3 gap-2"
															>
																<Upload className="w-4 h-4" />
																Selecionar PDF
															</label>
														</div>

														{selectedFile && (
															<Button
																size="sm"
																onClick={() => handleUploadPDF(prescription.id)}
																disabled={uploadingPrescription === prescription.id}
																className="flex items-center gap-2"
															>
																{uploadingPrescription === prescription.id ? (
																	<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
																) : (
																	<Upload className="w-4 h-4" />
																)}
																Anexar PDF
															</Button>
														)}
													</div>
												)}

												<Button
													variant="outline"
													size="sm"
													onClick={() => router.push(`/app/appointments/${prescription.appointmentId}/prescription`)}
													className="flex items-center gap-2"
												>
													<Edit className="w-4 h-4" />
													Editar
												</Button>
											</div>
										</div>
									</CardContent>
								</Card>
							))}
						</div>
					)}
				</TabsContent>

				<TabsContent value="active" className="space-y-4">
					{/* Filtrar apenas prescrições ativas */}
					<div className="grid gap-4">
						{filteredPrescriptions
							.filter(p => p.status === 'active')
							.map((prescription) => (
								<Card key={prescription.id}>
									<CardContent className="p-6">
										<div className="flex items-center justify-between">
											<div>
												<h3 className="font-medium">{prescription.patientName}</h3>
												<p className="text-sm text-gray-600">
													{new Date(prescription.createdAt).toLocaleDateString('pt-BR')}
												</p>
											</div>
											{getStatusBadge(prescription.status)}
										</div>
									</CardContent>
								</Card>
							))}
					</div>
				</TabsContent>

				<TabsContent value="pending" className="space-y-4">
					{/* Filtrar apenas prescrições sem PDF */}
					<div className="grid gap-4">
						{filteredPrescriptions
							.filter(p => !p.pdfUrl)
							.map((prescription) => (
								<Card key={prescription.id} className="border-orange-200">
									<CardContent className="p-6">
										<div className="flex items-center justify-between">
											<div>
												<h3 className="font-medium">{prescription.patientName}</h3>
												<p className="text-sm text-orange-600">
													PDF pendente de anexo
												</p>
											</div>
											<Badge variant="outline" className="text-orange-600 border-orange-200">
												Sem PDF
											</Badge>
										</div>
									</CardContent>
								</Card>
							))}
					</div>
				</TabsContent>
			</Tabs>
		</div>
	);
}

'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import {
  Wallet,
  Clock,
  TrendingUp,
  Eye,
  EyeOff,
  Download,
  History
} from "lucide-react";
import { useState } from "react";
import { cn } from "@ui/lib";

interface BalanceData {
  availableBalance: number;
  pendingBalance: number;
  totalEarnings: number;
  lastWithdrawal?: {
    amount: number;
    date: string;
    status: 'completed' | 'pending' | 'processing';
  };
  balanceHistory: Array<{
    date: string;
    amount: number;
    type: 'earning' | 'withdrawal' | 'fee';
    description: string;
  }>;
}

interface BalanceCardsProps {
  data: BalanceData;
  onRequestWithdrawal: () => void;
  onViewHistory: () => void;
  loading?: boolean;
}

export function BalanceCards({
  data,
  onRequestWithdrawal,
  onViewHistory,
  loading = false
}: BalanceCardsProps) {
  const [showBalance, setShowBalance] = useState(true);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(amount);
  };

  const formatBalanceDisplay = (amount: number) => {
    if (!showBalance) return '••••••';
    return formatCurrency(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'processing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Concluído';
      case 'processing':
        return 'Processando';
      case 'pending':
        return 'Pendente';
      default:
        return status;
    }
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-muted rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-muted rounded w-2/3"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Balance Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Available Balance */}
        <Card className="relative overflow-hidden border-l-4 border-l-green-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Saldo Disponível
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowBalance(!showBalance)}
                className="h-8 w-8 p-0"
              >
                {showBalance ? (
                  <Eye className="h-4 w-4" />
                ) : (
                  <EyeOff className="h-4 w-4" />
                )}
              </Button>
              <Wallet className="h-4 w-4 text-green-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-700 mb-2">
              {formatBalanceDisplay(data.availableBalance)}
            </div>
            <p className="text-xs text-muted-foreground mb-3">
              Valor disponível para saque
            </p>
            <div className="text-xs text-muted-foreground">
              Clique em "Solicitar Saque" no topo da página
            </div>
          </CardContent>
        </Card>

        {/* Pending Balance */}
        <Card className="relative overflow-hidden border-l-4 border-l-yellow-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Saldo Pendente
            </CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-700 mb-2">
              {formatBalanceDisplay(data.pendingBalance)}
            </div>
            <p className="text-xs text-muted-foreground mb-3">
              Aguardando processamento
            </p>
            <div className="flex items-center text-xs text-yellow-600">
              <Clock className="h-3 w-3 mr-1" />
              Liberação em até 2 dias úteis
            </div>
          </CardContent>
        </Card>

        {/* Total Earnings */}
        <Card className="relative overflow-hidden border-l-4 border-l-blue-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Ganhos Totais
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-700 mb-2">
              {formatBalanceDisplay(data.totalEarnings)}
            </div>
            <p className="text-xs text-muted-foreground mb-3">
              Receita total acumulada
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={onViewHistory}
              className="w-full"
            >
              <History className="h-4 w-4 mr-2" />
              Ver Histórico
            </Button>
          </CardContent>
        </Card>
      </div>


    </div>
  );
}

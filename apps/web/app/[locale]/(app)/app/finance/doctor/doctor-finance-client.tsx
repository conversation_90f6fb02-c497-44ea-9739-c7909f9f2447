'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { DollarSign, TrendingUp, Calendar, Percent, ArrowUpRight } from "lucide-react";
import { FinancialMetricsCard } from "../components/financial-metrics-card";
import { RevenueChart } from "../components/revenue-chart";
import { PaymentMethodChart } from "../components/payment-method-chart";
import { TransactionsTable } from "../components/transactions-table";
import { EnhancedTransactionsTable } from "../components/enhanced-transactions-table";
import { SimplifiedFinancialFilters } from "../components/simplified-financial-filters";
import { BalanceCards } from "../components/balance-cards";
import { WithdrawalRequestModal } from "../components/withdrawal-request-modal";
import { WithdrawalHistory } from "../components/withdrawal-history";

interface Transaction {
  id: string;
  amount: number;
  doctorAmount: number;
  platformFee: number;
  status: string;
  paymentMethod: string;
  paidAt?: string;
  createdAt: string;
  patientName?: string;
  appointmentDate?: string;
  description?: string;
  transactionId?: string;
}

interface DoctorFinanceData {
  metrics: {
    totalRevenue: number;
    monthlyRevenue: number;
    paidAppointments: number;
    conversionRate: number;
    averageTicket: number;
  };
  balance: {
    availableBalance: number;
    pendingBalance: number;
    totalEarnings: number;
    lastWithdrawal?: {
      amount: number;
      date: string;
      status: 'completed' | 'pending' | 'processing';
    };
    balanceHistory: Array<{
      date: string;
      amount: number;
      type: 'earning' | 'withdrawal' | 'fee';
      description: string;
    }>;
  };
  bankAccounts: Array<{
    id: string;
    bankName: string;
    accountType: string;
    accountNumber: string;
    agency: string;
    holderName: string;
    holderDocument: string;
    isDefault: boolean;
  }>;
  withdrawals: Array<{
    id: string;
    amount: number;
    netAmount: number;
    fee: number;
    status: 'pending' | 'processing' | 'completed' | 'rejected' | 'cancelled';
    requestedAt: string;
    processedAt?: string;
    completedAt?: string;
    bankAccount: {
      bankName: string;
      accountType: string;
      accountNumber: string;
      agency: string;
      holderName: string;
    };
    notes?: string;
    rejectionReason?: string;
    transactionId?: string;
  }>;
  analytics: {
    monthlyGrowth: {
      revenue: number;
      appointments: number;
      averageTicket: number;
    };
    topPerformingPeriods: Array<{
      period: string;
      revenue: number;
      appointments: number;
    }>;
    paymentMethodAnalysis: Array<{
      method: string;
      percentage: number;
      revenue: number;
      trend: 'up' | 'down' | 'stable';
    }>;
    revenueGoals: {
      monthly: {
        target: number;
        current: number;
        percentage: number;
      };
      yearly: {
        target: number;
        current: number;
        percentage: number;
      };
    };
    insights: Array<{
      type: 'positive' | 'negative' | 'neutral';
      title: string;
      description: string;
      value?: string;
    }>;
  };
  chartData: Array<{
    date: string;
    revenue: number;
    appointments: number;
  }>;
  paymentMethods: Array<{
    method: string;
    count: number;
    amount: number;
  }>;
  transactions: Array<{
    id: string;
    amount: number;
    doctorAmount: number;
    platformFee: number;
    status: string;
    paymentMethod: string;
    paidAt?: string;
    createdAt: string;
    patientName?: string;
    appointmentDate?: string;
  }>;
}

export function DoctorFinanceClient() {
  const [data, setData] = useState<DoctorFinanceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    period: '30days',
    status: 'all',
  });
  const [showWithdrawalModal, setShowWithdrawalModal] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);

  const fetchFinancialData = async () => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams(filters);
      const response = await fetch(`/api/finance/doctor?${params}`);

      if (!response.ok) {
        throw new Error('Erro ao carregar dados financeiros');
      }

      const result = await response.json();
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
      console.error('Error fetching financial data:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFinancialData();
  }, [filters]);

  const handleFiltersChange = (newFilters: typeof filters) => {
    setFilters(newFilters);
  };

  const handleWithdrawalRequest = async (withdrawalData: {
    amount: number;
    bankAccountId: string;
    notes?: string;
  }) => {
    try {
      const response = await fetch('/api/finance/doctor/withdrawal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(withdrawalData),
      });

      if (!response.ok) {
        throw new Error('Erro ao processar solicitação de saque');
      }

      // Refresh data after successful withdrawal request
      await fetchFinancialData();
      setShowWithdrawalModal(false);
    } catch (error) {
      throw error; // Re-throw to be handled by the modal
    }
  };

  // Preparar dados para exportação
  const prepareExportData = () => {
    if (!data?.transactions) return [];

    return data.transactions.map(t => ({
      'Data': new Date(t.createdAt).toLocaleDateString('pt-BR'),
      'Paciente': t.patientName || 'N/A',
      'Valor Total': `R$ ${t.amount.toFixed(2)}`,
      'Valor Médico': `R$ ${t.doctorAmount.toFixed(2)}`,
      'Taxa Plataforma': `R$ ${t.platformFee.toFixed(2)}`,
      'Status': t.status,
      'Método': t.paymentMethod,
      'Data Pagamento': t.paidAt ? new Date(t.paidAt).toLocaleDateString('pt-BR') : 'N/A',
      'Data Consulta': t.appointmentDate ? new Date(t.appointmentDate).toLocaleDateString('pt-BR') : 'N/A',
    }));
  };

  if (loading) {
    return (
      <div className="container mx-auto p-4 md:p-6 space-y-6">
        <div className="flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0">
          <div className="space-y-1">
            <h1 className="text-2xl md:text-3xl font-bold">Dashboard Financeiro</h1>
            <p className="text-sm md:text-base text-muted-foreground">
              Acompanhe suas receitas e transações
            </p>
          </div>
        </div>

        {/* Loading skeleton */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-4 md:p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-muted rounded w-1/2 mb-2"></div>
                  <div className="h-3 bg-muted rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Charts loading */}
        <div className="grid gap-4 lg:grid-cols-7">
          <Card className="lg:col-span-4">
            <CardContent className="p-4 md:p-6">
              <div className="animate-pulse">
                <div className="h-6 bg-muted rounded w-1/3 mb-4"></div>
                <div className="h-[300px] bg-muted rounded"></div>
              </div>
            </CardContent>
          </Card>
          <Card className="lg:col-span-3">
            <CardContent className="p-4 md:p-6">
              <div className="animate-pulse">
                <div className="h-6 bg-muted rounded w-1/2 mb-4"></div>
                <div className="h-[300px] bg-muted rounded"></div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h1 className="text-3xl font-bold">Dashboard Financeiro</h1>
            <p className="text-muted-foreground">
              Acompanhe suas receitas e transações
            </p>
          </div>
        </div>

        <Card>
          <CardContent className="p-6">
            <div className="text-center text-red-600">
              <p className="text-lg font-medium">Erro ao carregar dados</p>
              <p className="text-sm text-muted-foreground mt-2">{error}</p>
              <button
                onClick={fetchFinancialData}
                className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Tentar novamente
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 md:p-6 space-y-6">
      <div className="flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div className="space-y-1">
          <h1 className="text-2xl md:text-3xl font-bold">Dashboard Financeiro</h1>
          <p className="text-sm md:text-base text-muted-foreground">
            Acompanhe suas receitas e transações
          </p>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            onClick={() => setShowWithdrawalModal(true)}
            className="bg-green-600 hover:bg-green-700"
          >
            <ArrowUpRight className="h-4 w-4 mr-2" />
            Solicitar Saque
          </Button>
        </div>
      </div>

      {/* Balance Cards */}
      {data?.balance && (
        <BalanceCards
          data={data.balance}
          onRequestWithdrawal={() => setShowWithdrawalModal(true)}
          onViewHistory={() => {}}
          loading={loading}
        />
      )}



      {/* Métricas Principais */}
      {/* <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <FinancialMetricsCard
          title="Receita Total"
          value={data?.metrics.totalRevenue || 0}
          icon={DollarSign}
          color="green"
          trend="+12%"
          trendDirection="up"
          description="em relação ao mês anterior"
        />

        <FinancialMetricsCard
          title="Receita do Mês"
          value={data?.metrics.monthlyRevenue || 0}
          icon={TrendingUp}
          color="blue"
          trend="+8%"
          trendDirection="up"
          description="em relação ao mês anterior"
        />

        <FinancialMetricsCard
          title="Consultas Pagas"
          value={data?.metrics.paidAppointments || 0}
          icon={Calendar}
          color="purple"
          trend="+5%"
          trendDirection="up"
          description="em relação ao mês anterior"
        />

        <FinancialMetricsCard
          title="Taxa de Conversão"
          value={data?.metrics.conversionRate || 0}
          icon={Percent}
          color="orange"
          trend="+2%"
          trendDirection="up"
          description="em relação ao mês anterior"
        />
      </div> */}

      {/* Gráficos */}
      <div className="grid gap-4 lg:grid-cols-7">
        <Card className="lg:col-span-4">
          <CardHeader>
            <CardTitle className="text-lg md:text-xl">Evolução da Receita</CardTitle>
          </CardHeader>
          <CardContent className="p-4 md:p-6">
            <RevenueChart
              data={data?.chartData || []}
              showAppointments={true}
              height={280}
            />
          </CardContent>
        </Card>

        <Card className="lg:col-span-3">
          <CardHeader>
            <CardTitle className="text-lg md:text-xl">Métodos de Pagamento</CardTitle>
          </CardHeader>
          <CardContent className="p-4 md:p-6">
            <PaymentMethodChart
              data={data?.paymentMethods || []}
              showAmount={true}
              height={280}
            />
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Transactions Table */}
      <EnhancedTransactionsTable
        transactions={data?.transactions || []}
        showPatientColumn={true}
        showDoctorColumn={false}
        loading={loading}
        onRefresh={fetchFinancialData}
        onViewTransaction={(transaction) => setSelectedTransaction(transaction)}
        onExportTransaction={(transaction) => {
          // Handle transaction export
          console.log('Export transaction:', transaction);
        }}
      />

      {/* Withdrawal History - Always visible */}
      {data?.withdrawals && (
        <WithdrawalHistory
          withdrawals={data.withdrawals}
          loading={loading}
          onRefresh={fetchFinancialData}
        />
      )}

      {/* Withdrawal Request Modal */}
      <WithdrawalRequestModal
        isOpen={showWithdrawalModal}
        onClose={() => setShowWithdrawalModal(false)}
        availableBalance={data?.balance?.availableBalance || 0}
        bankAccounts={data?.bankAccounts || []}
        onSubmit={handleWithdrawalRequest}
      />
    </div>
  );
}

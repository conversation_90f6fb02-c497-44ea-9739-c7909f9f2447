"use client";

import { useRouter } from "next/navigation";
import { CreatePrescriptionForm } from "./components/create-prescription-form";

interface PrescriptionPageClientProps {
  appointmentId: string;
  patientName: string;
}

export function PrescriptionPageClient({ 
  appointmentId, 
  patientName 
}: PrescriptionPageClientProps) {
  const router = useRouter();

  const handleSuccess = () => {
    // Redirecionar para a página de prescrições após sucesso
    router.push("/app/prescriptions");
  };

  const handleCancel = () => {
    // Redirecionar de volta para prescrições
    router.push("/app/prescriptions");
  };

  return (
    <div className="container mx-auto py-6">
      <CreatePrescriptionForm
        appointmentId={appointmentId}
        patientName={patientName}
        onSuccess={handleSuccess}
        onCancel={handleCancel}
      />
    </div>
  );
}

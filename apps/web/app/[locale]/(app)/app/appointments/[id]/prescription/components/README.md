# Upload de PDF em Prescrições Médicas

Esta documentação descreve a implementação da funcionalidade de upload de PDF diretamente no formulário de criação de prescrições médicas.

## Visão Geral

A funcionalidade permite que médicos anexem receitas PDF durante o processo de criação da prescrição, eliminando a necessidade de fazer upload separadamente após a criação.

## Componentes

### PDFUploader

Componente reutilizável para upload de arquivos PDF com as seguintes funcionalidades:

- **Drag & Drop**: Arraste e solte arquivos PDF
- **Validação**: Verifica tipo de arquivo e tamanho
- **Preview**: Mostra informações do arquivo selecionado
- **Progress**: Barra de progresso durante upload
- **Error Handling**: Tratamento de erros com mensagens claras

#### Props

```typescript
interface PDFUploaderProps {
  onFileSelect: (file: File | null) => void;
  selectedFile: File | null;
  isUploading?: boolean;
  error?: string;
  maxSize?: number; // em MB, padrão: 10MB
  disabled?: boolean;
  className?: string;
}
```

#### Exemplo de Uso

```tsx
import { PDFUploader } from "./pdf-uploader";

function MyComponent() {
  const [selectedPDF, setSelectedPDF] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState("");

  return (
    <PDFUploader
      onFileSelect={setSelectedPDF}
      selectedFile={selectedPDF}
      isUploading={isUploading}
      error={error}
      maxSize={10}
    />
  );
}
```

### PDFPreview

Componente para visualizar e gerenciar o arquivo PDF selecionado:

- **Informações do Arquivo**: Nome e tamanho
- **Preview**: Visualização do PDF em iframe
- **Ações**: Download, substituir e remover arquivo

#### Props

```typescript
interface PDFPreviewProps {
  file: File;
  onRemove: () => void;
  onReplace: (file: File) => void;
  className?: string;
}
```

## Fluxo de Implementação

### 1. Formulário de Criação

O formulário `CreatePrescriptionForm` foi modificado para incluir:

```typescript
// Estados para PDF
const [selectedPDF, setSelectedPDF] = useState<File | null>(null);
const [pdfError, setPdfError] = useState<string>("");
const [isUploadingPDF, setIsUploadingPDF] = useState(false);

// Funções de gerenciamento
const handlePDFSelect = (file: File | null) => {
  setSelectedPDF(file);
  setPdfError("");
};
```

### 2. Envio de Dados

O formulário agora usa `FormData` em vez de JSON para suportar upload de arquivos:

```typescript
const formData = new FormData();
formData.append('appointmentId', appointmentId);
formData.append('medications', JSON.stringify(validMedications));
formData.append('instructions', generalInstructions.trim() || '');
formData.append('validUntil', validUntil || '');

// Adicionar PDF se selecionado
if (selectedPDF) {
  formData.append('pdf', selectedPDF);
}
```

### 3. API de Criação

A API `/api/prescriptions/create` foi atualizada para processar tanto JSON quanto FormData:

```typescript
// Detectar tipo de conteúdo
const contentType = request.headers.get("content-type");

if (contentType?.includes("multipart/form-data")) {
  // Processar FormData com PDF
  const formData = await request.formData();
  const pdfFile = formData.get("pdf") as File | null;
  
  if (pdfFile && pdfFile.size > 0) {
    // Validar e fazer upload do PDF
    const uploadResult = await uploadFile(filename, arrayBuffer, options);
    pdfUrl = uploadResult.url;
  }
} else {
  // Processar JSON (compatibilidade)
  const body = await request.json();
}
```

## Validações

### Arquivo PDF

- **Tipo**: Apenas `application/pdf`
- **Tamanho**: Máximo 10MB
- **Conteúdo**: Arquivo não pode estar vazio

### Segurança

- **Autenticação**: Apenas médicos autenticados
- **Autorização**: Médico deve ser dono da consulta
- **Sanitização**: Nome do arquivo é sanitizado
- **Storage**: Upload seguro para S3

## Estrutura de Dados

### Banco de Dados

O modelo `Prescription` já possui o campo `pdfUrl`:

```prisma
model Prescription {
  id            String      @id @default(cuid())
  appointmentId String      @unique
  appointment   Appointment @relation(fields: [appointmentId], references: [id])
  memedId       String?
  content       Json?
  pdfUrl        String?     // URL do PDF
  status        String      @default("active")
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
}
```

### Storage S3

Arquivos são salvos com a estrutura:

```
prescriptions/
  {appointmentId}/
    {timestamp}_prescricao_{patientName}.pdf
```

## Tratamento de Erros

### Validação de Arquivo

```typescript
const validateFile = (file: File): string | null => {
  if (file.type !== "application/pdf") {
    return "Apenas arquivos PDF são permitidos";
  }
  
  if (file.size > 10 * 1024 * 1024) {
    return "Arquivo muito grande. Máximo 10MB permitido";
  }
  
  if (file.size === 0) {
    return "Arquivo está vazio ou corrompido";
  }
  
  return null;
};
```

### Erros de Upload

- **Arquivo inválido**: Mensagem específica sobre o problema
- **Falha no upload**: Erro genérico com log detalhado
- **Timeout**: Tratamento de timeout de rede
- **Storage**: Erros de conexão com S3

## Compatibilidade

A implementação mantém compatibilidade com:

- **Prescrições existentes**: Sem PDF continuam funcionando
- **API anterior**: Ainda aceita JSON sem PDF
- **Upload posterior**: Funcionalidade existente de upload separado

## Testes

### Testes Unitários

```bash
# Executar testes
npm test pdf-upload.test.ts
```

### Testes de Integração

1. **Upload bem-sucedido**: PDF válido é anexado
2. **Validação de arquivo**: Arquivos inválidos são rejeitados
3. **Erro de rede**: Tratamento de falhas de conexão
4. **Compatibilidade**: JSON ainda funciona

## Monitoramento

### Logs

- Upload de PDF é logado com detalhes
- Erros são capturados e reportados
- Métricas de uso são coletadas

### Métricas

- Taxa de sucesso de uploads
- Tamanho médio dos arquivos
- Tempo de upload
- Erros mais comuns

## Próximos Passos

### Melhorias Futuras

1. **Compressão**: Reduzir tamanho dos PDFs automaticamente
2. **OCR**: Extrair texto dos PDFs para busca
3. **Assinatura Digital**: Validar assinaturas digitais
4. **QR Code**: Gerar QR codes para validação
5. **Notificações**: Avisar paciente quando PDF é anexado

### Otimizações

1. **Lazy Loading**: Carregar componentes sob demanda
2. **Caching**: Cache de previews de PDF
3. **CDN**: Distribuição via CDN para downloads
4. **Thumbnails**: Gerar miniaturas dos PDFs

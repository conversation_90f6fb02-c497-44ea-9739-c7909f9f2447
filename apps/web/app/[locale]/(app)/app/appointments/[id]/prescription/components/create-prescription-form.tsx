"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Textarea } from "@ui/components/textarea";
import { Badge } from "@ui/components/badge";
import { Plus, Trash2, Save, FileText, Upload } from "lucide-react";
import { toast } from "sonner";
import { PDFUploader } from "./pdf-uploader";
import { PDFPreview } from "./pdf-preview";

interface Medication {
  name: string;
  dosage: string;
  frequency: string;
  duration: string;
  instructions?: string;
}

interface CreatePrescriptionFormProps {
  appointmentId: string;
  patientName: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function CreatePrescriptionForm({
  appointmentId,
  patientName,
  onSuccess,
  onCancel
}: CreatePrescriptionFormProps) {
  const [medications, setMedications] = useState<Medication[]>([
    { name: "", dosage: "", frequency: "", duration: "", instructions: "" }
  ]);
  const [generalInstructions, setGeneralInstructions] = useState("");
  const [validUntil, setValidUntil] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Estados para upload de PDF
  const [selectedPDF, setSelectedPDF] = useState<File | null>(null);
  const [pdfError, setPdfError] = useState<string>("");
  const [isUploadingPDF, setIsUploadingPDF] = useState(false);

  const addMedication = () => {
    setMedications([
      ...medications,
      { name: "", dosage: "", frequency: "", duration: "", instructions: "" }
    ]);
  };

  const removeMedication = (index: number) => {
    if (medications.length > 1) {
      setMedications(medications.filter((_, i) => i !== index));
    }
  };

  const updateMedication = (index: number, field: keyof Medication, value: string) => {
    const updated = medications.map((med, i) =>
      i === index ? { ...med, [field]: value } : med
    );
    setMedications(updated);
  };

  // Funções para gerenciar PDF
  const handlePDFSelect = (file: File | null) => {
    setSelectedPDF(file);
    setPdfError("");
  };

  const handlePDFRemove = () => {
    setSelectedPDF(null);
    setPdfError("");
  };

  const handlePDFReplace = (file: File) => {
    setSelectedPDF(file);
    setPdfError("");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validar medicamentos
    const validMedications = medications.filter(med =>
      med.name.trim() && med.dosage.trim() && med.frequency.trim() && med.duration.trim()
    );

    if (validMedications.length === 0) {
      toast.error("Adicione pelo menos um medicamento válido");
      return;
    }

    setIsSubmitting(true);
    setIsUploadingPDF(true);

    try {
      // Criar FormData para enviar dados e arquivo
      const formData = new FormData();
      formData.append('appointmentId', appointmentId);
      formData.append('medications', JSON.stringify(validMedications));
      formData.append('instructions', generalInstructions.trim() || '');
      formData.append('validUntil', validUntil || '');

      // Adicionar PDF se selecionado
      if (selectedPDF) {
        formData.append('pdf', selectedPDF);
      }

      const response = await fetch('/api/prescriptions/create', {
        method: 'POST',
        body: formData // Usar FormData em vez de JSON
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao criar prescrição');
      }

      const data = await response.json();
      toast.success("Prescrição criada com sucesso!");

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Erro ao criar prescrição:', error);
      toast.error(error instanceof Error ? error.message : "Erro ao criar prescrição");
    } finally {
      setIsSubmitting(false);
      setIsUploadingPDF(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5 text-blue-600" />
            Nova Prescrição Médica
          </CardTitle>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Paciente:</span>
            <Badge variant="outline">{patientName}</Badge>
          </div>
        </CardHeader>
      </Card>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Medicamentos */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Medicamentos</CardTitle>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addMedication}
                className="flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                Adicionar Medicamento
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {medications.map((medication, index) => (
              <div key={index} className="p-4 border rounded-lg space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-gray-900">
                    Medicamento {index + 1}
                  </h4>
                  {medications.length > 1 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeMedication(index)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Nome do Medicamento *
                    </label>
                    <Input
                      value={medication.name}
                      onChange={(e) => updateMedication(index, 'name', e.target.value)}
                      placeholder="Ex: Dipirona Sódica"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Dosagem *
                    </label>
                    <Input
                      value={medication.dosage}
                      onChange={(e) => updateMedication(index, 'dosage', e.target.value)}
                      placeholder="Ex: 500mg"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Frequência *
                    </label>
                    <Input
                      value={medication.frequency}
                      onChange={(e) => updateMedication(index, 'frequency', e.target.value)}
                      placeholder="Ex: 3 vezes ao dia"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Duração *
                    </label>
                    <Input
                      value={medication.duration}
                      onChange={(e) => updateMedication(index, 'duration', e.target.value)}
                      placeholder="Ex: 7 dias"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Instruções Específicas
                  </label>
                  <Textarea
                    value={medication.instructions}
                    onChange={(e) => updateMedication(index, 'instructions', e.target.value)}
                    placeholder="Ex: Tomar com alimentos, evitar álcool..."
                    rows={2}
                  />
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Instruções Gerais */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Instruções Gerais</CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              value={generalInstructions}
              onChange={(e) => setGeneralInstructions(e.target.value)}
              placeholder="Instruções gerais para o paciente (repouso, dieta, retorno, etc.)"
              rows={4}
            />
          </CardContent>
        </Card>

        {/* Validade */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Validade da Prescrição</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="max-w-md">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Válida até (opcional)
              </label>
              <Input
                type="date"
                value={validUntil}
                onChange={(e) => setValidUntil(e.target.value)}
                min={new Date().toISOString().split('T')[0]}
              />
              <p className="text-xs text-gray-500 mt-1">
                Se não especificado, será válida por 30 dias
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Upload de PDF */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <Upload className="w-5 h-5 text-blue-600" />
              Anexar Receita PDF (Opcional)
            </CardTitle>
            <p className="text-sm text-gray-600 mt-1">
              Você pode anexar uma receita em PDF assinada digitalmente
            </p>
          </CardHeader>
          <CardContent>
            {!selectedPDF ? (
              <PDFUploader
                onFileSelect={handlePDFSelect}
                selectedFile={selectedPDF}
                isUploading={isUploadingPDF}
                error={pdfError}
                disabled={isSubmitting}
              />
            ) : (
              <PDFPreview
                file={selectedPDF}
                onRemove={handlePDFRemove}
                onReplace={handlePDFReplace}
              />
            )}
          </CardContent>
        </Card>

        {/* Ações */}
        <div className="flex items-center justify-end gap-4">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancelar
            </Button>
          )}
          <Button
            type="submit"
            disabled={isSubmitting}
            className="flex items-center gap-2"
          >
            {isSubmitting ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Save className="w-4 h-4" />
            )}
            {isSubmitting ? "Criando..." : "Criar Prescrição"}
          </Button>
        </div>
      </form>
    </div>
  );
}

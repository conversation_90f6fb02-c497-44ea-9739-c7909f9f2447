import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";
import { redirect } from "next/navigation";
import { CreatePrescriptionForm } from "./components/create-prescription-form";
import { PrescriptionPageClient } from "./prescription-page-client";

interface PrescriptionPageProps {
  params: {
    id: string;
  };
}

async function getAppointmentData(appointmentId: string, userId: string) {
  const appointment = await db.appointment.findUnique({
    where: { id: appointmentId },
    include: {
      patient: {
        include: {
          user: true
        }
      },
      doctor: {
        include: {
          user: true
        }
      },
      prescription: true
    }
  });

  if (!appointment) {
    return null;
  }

  // Verificar se o usuário tem acesso a esta consulta
  const isDoctor = appointment.doctor?.userId === userId;
  const isPatient = appointment.patient.userId === userId;
  
  if (!isDoctor && !isPatient) {
    return null;
  }

  return appointment;
}

export default async function PrescriptionPage({ params }: PrescriptionPageProps) {
  const { user } = await currentUser();

  if (!user) {
    redirect("/auth/login");
  }

  // Verificar se é médico
  if (user.role !== "DOCTOR" && user.role !== "ADMIN") {
    redirect("/app/dashboard");
  }

  const appointment = await getAppointmentData(params.id, user.id);

  if (!appointment) {
    redirect("/app/appointments");
  }

  // Se já existe prescrição, redirecionar para visualização
  if (appointment.prescription) {
    redirect(`/app/prescriptions/${appointment.prescription.id}`);
  }

  return (
    <PrescriptionPageClient
      appointmentId={appointment.id}
      patientName={appointment.patient.user.name || "Paciente"}
    />
  );
}

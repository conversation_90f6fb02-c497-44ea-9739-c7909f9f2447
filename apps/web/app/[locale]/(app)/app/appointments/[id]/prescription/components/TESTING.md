# Guia de Testes - Upload de PDF em Prescrições

Este guia fornece instruções para testar a nova funcionalidade de upload de PDF no formulário de criação de prescrições.

## Pré-requisitos

1. **Ambiente de desenvolvimento** rodando
2. **Usuário médico** autenticado
3. **Consulta agendada** disponível
4. **Arquivos PDF de teste** preparados

## Cenários de Teste

### 1. Upload Bem-sucedido

**Objetivo**: Verificar se o upload de PDF funciona corretamente

**Passos**:
1. Acesse uma consulta como médico
2. Clique em "Criar Prescrição"
3. Preencha os medicamentos obrigatórios
4. Na seção "Anexar Receita PDF":
   - Arraste um PDF válido (< 10MB) para a área de upload
   - OU clique para selecionar arquivo
5. Verifique se o preview aparece
6. Preencha outros campos opcionais
7. Clique em "Criar Prescrição"

**Resultado Esperado**:
- PDF é aceito sem erros
- Preview mostra informações corretas
- Prescrição é criada com sucesso
- Toast de sucesso é exibido
- PDF fica disponível para download

### 2. Validação de Tipo de Arquivo

**Objetivo**: Verificar se apenas PDFs são aceitos

**Passos**:
1. Tente fazer upload de arquivo não-PDF (.txt, .doc, .jpg)
2. Observe a mensagem de erro

**Resultado Esperado**:
- Erro: "Apenas arquivos PDF são permitidos"
- Arquivo não é aceito
- Upload não prossegue

### 3. Validação de Tamanho

**Objetivo**: Verificar limite de 10MB

**Passos**:
1. Tente fazer upload de PDF > 10MB
2. Observe a mensagem de erro

**Resultado Esperado**:
- Erro: "Arquivo muito grande. Máximo 10MB permitido"
- Arquivo não é aceito

### 4. Arquivo Vazio

**Objetivo**: Verificar rejeição de arquivos vazios

**Passos**:
1. Crie um arquivo PDF vazio (0 bytes)
2. Tente fazer upload

**Resultado Esperado**:
- Erro: "Arquivo está vazio ou corrompido"
- Arquivo não é aceito

### 5. Drag & Drop

**Objetivo**: Testar funcionalidade de arrastar e soltar

**Passos**:
1. Arraste um PDF válido para a área de upload
2. Observe o feedback visual
3. Solte o arquivo

**Resultado Esperado**:
- Área muda de cor durante drag over
- Arquivo é aceito quando solto
- Preview aparece

### 6. Preview e Ações

**Objetivo**: Testar funcionalidades do preview

**Passos**:
1. Selecione um PDF
2. Clique em "Visualizar" no preview
3. Clique em "Download"
4. Clique em "Substituir"
5. Clique em "Remover"

**Resultado Esperado**:
- Visualização abre em iframe
- Download funciona
- Substituição permite selecionar novo arquivo
- Remoção limpa o arquivo selecionado

### 7. Compatibilidade com Versão Anterior

**Objetivo**: Verificar se prescrições sem PDF ainda funcionam

**Passos**:
1. Crie prescrição sem selecionar PDF
2. Preencha apenas medicamentos e instruções
3. Submeta o formulário

**Resultado Esperado**:
- Prescrição é criada normalmente
- Não há erros relacionados ao PDF
- Funcionalidade anterior mantida

### 8. Erro de Rede

**Objetivo**: Testar comportamento com falha de upload

**Passos**:
1. Desconecte a internet (ou simule erro de rede)
2. Tente criar prescrição com PDF
3. Observe o tratamento de erro

**Resultado Esperado**:
- Erro é capturado e exibido
- Toast de erro aparece
- Formulário não trava

### 9. Prescrição Duplicada

**Objetivo**: Verificar se não permite prescrição duplicada

**Passos**:
1. Crie uma prescrição para uma consulta
2. Tente criar outra prescrição para a mesma consulta

**Resultado Esperado**:
- Erro: "Já existe uma prescrição para esta consulta"
- Segunda prescrição não é criada

### 10. Autorização

**Objetivo**: Verificar se apenas o médico da consulta pode criar prescrição

**Passos**:
1. Como médico A, tente criar prescrição para consulta do médico B
2. Observe o erro de autorização

**Resultado Esperado**:
- Erro: "Acesso negado a esta consulta"
- Prescrição não é criada

## Arquivos de Teste

### PDFs Válidos
- **Pequeno**: 1-2 MB, conteúdo simples
- **Médio**: 5-8 MB, várias páginas
- **Limite**: Exatamente 10 MB

### PDFs Inválidos
- **Grande**: > 10 MB
- **Vazio**: 0 bytes
- **Corrompido**: Arquivo com extensão .pdf mas conteúdo inválido

### Outros Arquivos
- **Texto**: arquivo.txt
- **Imagem**: imagem.jpg
- **Documento**: documento.docx

## Verificações Pós-Upload

### 1. Banco de Dados
```sql
SELECT id, appointmentId, pdfUrl, status, createdAt 
FROM prescriptions 
WHERE appointmentId = 'ID_DA_CONSULTA';
```

### 2. Storage S3
- Verificar se arquivo foi salvo no bucket correto
- Confirmar estrutura de pastas: `prescriptions/{appointmentId}/`
- Validar metadados do arquivo

### 3. Logs
- Verificar logs de upload no servidor
- Confirmar ausência de erros não tratados
- Validar métricas de performance

## Casos Extremos

### 1. Upload Simultâneo
- Múltiplos médicos fazendo upload ao mesmo tempo
- Verificar se não há conflitos

### 2. Sessão Expirada
- Upload durante sessão expirada
- Verificar redirecionamento para login

### 3. Permissões de Storage
- Falha de permissão no S3
- Verificar tratamento de erro

### 4. Limite de Banda
- Upload com conexão lenta
- Verificar timeout e retry

## Ferramentas de Teste

### 1. DevTools
- Network tab para monitorar requests
- Console para verificar erros JavaScript
- Application tab para verificar storage local

### 2. Postman/Insomnia
- Testar API diretamente
- Simular diferentes payloads
- Verificar responses

### 3. Logs do Servidor
```bash
# Monitorar logs em tempo real
tail -f logs/app.log | grep prescription

# Filtrar erros
grep ERROR logs/app.log | grep prescription
```

## Checklist de Validação

- [ ] Upload de PDF válido funciona
- [ ] Validação de tipo de arquivo
- [ ] Validação de tamanho (10MB)
- [ ] Rejeição de arquivo vazio
- [ ] Drag & drop funcional
- [ ] Preview com todas as ações
- [ ] Compatibilidade sem PDF
- [ ] Tratamento de erros de rede
- [ ] Prevenção de duplicatas
- [ ] Controle de autorização
- [ ] Arquivo salvo no S3
- [ ] URL correta no banco
- [ ] Logs sem erros
- [ ] Performance aceitável
- [ ] Interface responsiva

## Relatório de Bugs

Ao encontrar problemas, documente:

1. **Descrição**: O que aconteceu
2. **Passos**: Como reproduzir
3. **Esperado**: O que deveria acontecer
4. **Ambiente**: Browser, OS, versão
5. **Logs**: Erros no console/servidor
6. **Screenshots**: Se aplicável

## Contato

Para dúvidas ou problemas:
- Abra uma issue no repositório
- Contate a equipe de desenvolvimento
- Consulte a documentação técnica

import { NextRequest, NextResponse } from "next/server";
import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";
import { uploadFile } from "storage";

export async function POST(request: NextRequest) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    // Verificar se é médico ou admin
    if (user.role !== "DOCTOR" && user.role !== "ADMIN") {
      return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
    }

    // Verificar se é FormData (com PDF) ou JSON (sem PDF)
    const contentType = request.headers.get("content-type");
    let appointmentId: string;
    let medications: any[];
    let instructions: string | null;
    let validUntil: string | null;
    let pdfFile: File | null = null;

    if (contentType?.includes("multipart/form-data")) {
      // Processar FormData
      const formData = await request.formData();
      appointmentId = formData.get("appointmentId") as string;
      medications = JSON.parse(formData.get("medications") as string);
      instructions = formData.get("instructions") as string || null;
      validUntil = formData.get("validUntil") as string || null;
      pdfFile = formData.get("pdf") as File | null;
    } else {
      // Processar JSON (compatibilidade com versão anterior)
      const body = await request.json();
      appointmentId = body.appointmentId;
      medications = body.medications;
      instructions = body.instructions;
      validUntil = body.validUntil;
    }

    // Validar dados obrigatórios
    if (!appointmentId || !medications || !Array.isArray(medications)) {
      return NextResponse.json(
        { error: "appointmentId e medications são obrigatórios" },
        { status: 400 }
      );
    }

    // Verificar se a consulta existe e pertence ao médico
    const appointment = await db.appointment.findUnique({
      where: { id: appointmentId },
      include: {
        doctor: true,
        patient: {
          include: {
            user: true
          }
        }
      }
    });

    if (!appointment) {
      return NextResponse.json({ error: "Consulta não encontrada" }, { status: 404 });
    }

    // Verificar se o médico é o dono da consulta (ou se é admin)
    if (user.role === "DOCTOR" && appointment.doctor?.userId !== user.id) {
      return NextResponse.json({ error: "Acesso negado a esta consulta" }, { status: 403 });
    }

    // Verificar se já existe prescrição para esta consulta
    const existingPrescription = await db.prescription.findUnique({
      where: { appointmentId }
    });

    if (existingPrescription) {
      return NextResponse.json({ error: "Já existe uma prescrição para esta consulta" }, { status: 400 });
    }

    // Processar upload de PDF se fornecido
    let pdfUrl: string | null = null;
    if (pdfFile && pdfFile.size > 0) {
      try {
        // Validar arquivo PDF
        if (pdfFile.type !== "application/pdf") {
          return NextResponse.json({ error: "Apenas arquivos PDF são permitidos" }, { status: 400 });
        }

        // Verificar tamanho (máximo 10MB)
        if (pdfFile.size > 10 * 1024 * 1024) {
          return NextResponse.json({ error: "Arquivo muito grande. Máximo 10MB" }, { status: 400 });
        }

        // Gerar nome único para o arquivo
        const timestamp = Date.now();
        const patientName = appointment.patient.user.name?.replace(/[^a-zA-Z0-9]/g, '_') || 'paciente';
        const filename = `prescriptions/${appointmentId}/${timestamp}_prescricao_${patientName}.pdf`;

        // Converter File para ArrayBuffer
        const arrayBuffer = await pdfFile.arrayBuffer();

        // Fazer upload para S3
        const uploadResult = await uploadFile(filename, arrayBuffer, {
          bucket: process.env.NEXT_PUBLIC_PRESCRIPTIONS_BUCKET_NAME || "uploads",
          contentType: "application/pdf",
          metadata: {
            appointmentId,
            originalName: pdfFile.name,
            uploadedAt: new Date().toISOString(),
            uploadedBy: user.id,
          },
        });

        pdfUrl = uploadResult.url;
      } catch (uploadError) {
        console.error("Erro no upload do PDF:", uploadError);
        return NextResponse.json({ error: "Erro ao fazer upload do PDF" }, { status: 500 });
      }
    }

    // Criar prescrição
    const prescription = await db.prescription.create({
      data: {
        appointmentId,
        content: {
          medications,
          instructions: instructions || null,
          validUntil: validUntil || null,
          createdBy: user.id,
          createdAt: new Date().toISOString()
        },
        status: "active",
        pdfUrl: pdfUrl
      },
      include: {
        appointment: {
          include: {
            doctor: {
              include: {
                user: true,
                specialties: true
              }
            },
            patient: {
              include: {
                user: true
              }
            }
          }
        }
      }
    });

    // Formatar resposta
    const formattedPrescription = {
      id: prescription.id,
      appointmentId: prescription.appointmentId,
      patientName: prescription.appointment.patient.user.name,
      patientId: prescription.appointment.patient.id,
      doctorName: prescription.appointment.doctor?.user.name || "Médico",
      doctorCrm: prescription.appointment.doctor?.crm,
      doctorSpecialty: prescription.appointment.doctor?.specialties?.[0]?.name,
      createdAt: prescription.createdAt.toISOString(),
      updatedAt: prescription.updatedAt.toISOString(),
      status: prescription.status,
      content: prescription.content,
      pdfUrl: prescription.pdfUrl,
      memedId: prescription.memedId
    };

    return NextResponse.json({
      success: true,
      prescription: formattedPrescription
    }, { status: 201 });

  } catch (error) {
    console.error("Erro ao criar prescrição:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
}

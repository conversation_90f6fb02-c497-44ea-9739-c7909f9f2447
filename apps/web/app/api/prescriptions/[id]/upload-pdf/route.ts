import { NextRequest, NextResponse } from "next/server";
import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";
import { uploadFile } from "storage";

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    // Verificar se é médico ou admin
    if (user.role !== "DOCTOR" && user.role !== "ADMIN") {
      return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
    }

    const prescriptionId = params.id;

    // Verificar se a prescrição existe
    const prescription = await db.prescription.findUnique({
      where: { id: prescriptionId },
      include: {
        appointment: {
          include: {
            doctor: true
          }
        }
      }
    });

    if (!prescription) {
      return NextResponse.json({ error: "Prescrição não encontrada" }, { status: 404 });
    }

    // Verificar se o médico é o dono da prescrição (ou se é admin)
    if (user.role === "DOCTOR" && prescription.appointment.doctor?.userId !== user.id) {
      return NextResponse.json({ error: "Acesso negado a esta prescrição" }, { status: 403 });
    }

    const formData = await request.formData();
    const file = formData.get("pdf") as File;

    if (!file) {
      return NextResponse.json({ error: "Arquivo PDF é obrigatório" }, { status: 400 });
    }

    // Verificar se é PDF
    if (file.type !== "application/pdf") {
      return NextResponse.json({ error: "Apenas arquivos PDF são permitidos" }, { status: 400 });
    }

    // Verificar tamanho (máximo 10MB)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json({ error: "Arquivo muito grande. Máximo 10MB" }, { status: 400 });
    }

    // Upload para S3 usando o sistema de storage existente
    const filename = `prescriptions/${prescriptionId}/${Date.now()}_${file.name}`;

    // Converter File para ArrayBuffer
    const arrayBuffer = await file.arrayBuffer();

    // Fazer upload para S3
    const uploadResult = await uploadFile(filename, arrayBuffer, {
      bucket: process.env.NEXT_PUBLIC_PRESCRIPTIONS_BUCKET_NAME || "uploads",
      contentType: "application/pdf",
      metadata: {
        prescriptionId,
        originalName: file.name,
        uploadedAt: new Date().toISOString(),
      },
    });

    // Atualizar prescrição com URL do PDF
    const updatedPrescription = await db.prescription.update({
      where: { id: prescriptionId },
      data: {
        pdfUrl: uploadResult.url,
        updatedAt: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      prescription: updatedPrescription,
      pdfUrl: uploadResult.url
    });

  } catch (error) {
    console.error("Erro ao fazer upload do PDF:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    // Verificar se é médico ou admin
    if (user.role !== "DOCTOR" && user.role !== "ADMIN") {
      return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
    }

    const prescriptionId = params.id;

    // Verificar se a prescrição existe
    const prescription = await db.prescription.findUnique({
      where: { id: prescriptionId },
      include: {
        appointment: {
          include: {
            doctor: true
          }
        }
      }
    });

    if (!prescription) {
      return NextResponse.json({ error: "Prescrição não encontrada" }, { status: 404 });
    }

    // Verificar se o médico é o dono da prescrição (ou se é admin)
    if (user.role === "DOCTOR" && prescription.appointment.doctor?.userId !== user.id) {
      return NextResponse.json({ error: "Acesso negado a esta prescrição" }, { status: 403 });
    }

    // Remover URL do PDF
    const updatedPrescription = await db.prescription.update({
      where: { id: prescriptionId },
      data: {
        pdfUrl: null,
        updatedAt: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      prescription: updatedPrescription
    });

  } catch (error) {
    console.error("Erro ao remover PDF:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
}

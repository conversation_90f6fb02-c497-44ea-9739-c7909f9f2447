// Test script to verify prescription routes exist
// Run this with: node test-prescription-routes.js

const fs = require('fs');
const path = require('path');

const BASE_PATH = 'apps/web/app/[locale]/(app)/app';

function checkRouteExists(routePath) {
  const fullPath = path.join(BASE_PATH, routePath);
  const exists = fs.existsSync(fullPath);
  console.log(`${exists ? '✅' : '❌'} ${routePath} ${exists ? 'EXISTS' : 'MISSING'}`);
  return exists;
}

function checkFileExists(filePath) {
  const exists = fs.existsSync(filePath);
  console.log(`${exists ? '✅' : '❌'} ${filePath} ${exists ? 'EXISTS' : 'MISSING'}`);
  return exists;
}

console.log('🔍 Checking Prescription Routes...\n');

console.log('📁 Main Routes:');
checkRouteExists('prescriptions/page.tsx');
checkRouteExists('prescriptions/[id]/page.tsx');
checkRouteExists('appointments/[id]/prescription/page.tsx');

console.log('\n📁 Components:');
checkFileExists('apps/web/app/[locale]/(app)/app/prescriptions/components/doctor-prescriptions-client.tsx');
checkFileExists('apps/web/app/[locale]/(app)/app/appointments/[id]/prescription/components/create-prescription-form.tsx');
checkFileExists('apps/web/app/[locale]/(app)/app/appointments/[id]/prescription/prescription-page-client.tsx');

console.log('\n📁 API Routes:');
checkFileExists('apps/web/app/api/prescriptions/doctor/route.ts');
checkFileExists('apps/web/app/api/prescriptions/create/route.ts');
checkFileExists('apps/web/app/api/appointments/without-prescription/route.ts');

console.log('\n🎯 Route Structure Analysis:');
console.log('Expected URLs:');
console.log('  📋 /app/prescriptions - List all prescriptions');
console.log('  👁️  /app/prescriptions/[id] - View specific prescription');
console.log('  ➕ /app/appointments/[id]/prescription - Create new prescription');

console.log('\n🔗 Navigation Flow:');
console.log('  1. User visits /app/prescriptions');
console.log('  2. Clicks "Nova Prescrição" → Opens dialog with appointments');
console.log('  3. Selects appointment → Navigates to /app/appointments/[id]/prescription');
console.log('  4. Creates prescription → Redirects back to /app/prescriptions');
console.log('  5. Clicks "Visualizar" → Navigates to /app/prescriptions/[id]');

console.log('\n✨ All routes should now be properly configured!');

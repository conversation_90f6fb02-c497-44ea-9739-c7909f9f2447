// Simple test script to verify the prescriptions API endpoints
// Run this with: node test-prescriptions-api.js

const BASE_URL = 'http://localhost:3000';

async function testAPI() {
  console.log('Testing Prescriptions API endpoints...\n');

  try {
    // Test 1: Doctor prescriptions endpoint
    console.log('1. Testing /api/prescriptions/doctor');
    const doctorResponse = await fetch(`${BASE_URL}/api/prescriptions/doctor`);
    console.log(`Status: ${doctorResponse.status}`);
    
    if (doctorResponse.status === 401) {
      console.log('✓ Correctly returns 401 (Unauthorized) - authentication required');
    } else {
      const doctorData = await doctorResponse.json();
      console.log('Response:', JSON.stringify(doctorData, null, 2));
    }

    // Test 2: Appointments without prescription endpoint
    console.log('\n2. Testing /api/appointments/without-prescription');
    const appointmentsResponse = await fetch(`${BASE_URL}/api/appointments/without-prescription`);
    console.log(`Status: ${appointmentsResponse.status}`);
    
    if (appointmentsResponse.status === 401) {
      console.log('✓ Correctly returns 401 (Unauthorized) - authentication required');
    } else {
      const appointmentsData = await appointmentsResponse.json();
      console.log('Response:', JSON.stringify(appointmentsData, null, 2));
    }

    console.log('\n✅ API endpoints are responding correctly');
    console.log('Note: 401 responses are expected when not authenticated');

  } catch (error) {
    console.error('❌ Error testing API:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Make sure the development server is running:');
      console.log('   cd apps/web && npm run dev');
    }
  }
}

testAPI();
